import { createRouter, createWebHistory } from 'vue-router'
import aiSzAgentRoutes from '../cards/ai-sz-agent/router'
import gesiXiehuiRoutes from '../cards/GesiXiehui/router'
import linTianKeJiRoutes from '../cards/LinTianKeJi/router'
import linTianKeJi2Routes from '../cards/LinTianKeJi2/router'
import wanWangKeJiRoutes from '../cards/WanWangKeJi/router'
import aiSzGuideRoutes from '../cards/ai-sz-guide/router'
import xingTuAIRoutes from '../cards/XingTuAI/router'
const router = createRouter({
  history: createWebHistory(),
  routes: [
    // 主页路由 - 项目选择页面
    {
      path: '/',
      name: 'projectSelection',
      component: () => import('../views/ProjectSelectionView.vue'),
      meta: {
        title: '项目选择 - 项目名片中心'
      }
    },
    // 将AI数智代言人的路由添加到 /card/ai-sz-agent 前缀下
    ...aiSzAgentRoutes.map(route => ({
      ...route,
      path: `/card/ai-sz-agent${route.path}`
    })),
    // 将个私协会的路由添加到 /card/GesiXiehui 前缀下
    ...gesiXiehuiRoutes.map(route => ({
      ...route,
      path: `/card/GesiXiehui${route.path}`
    })),
    // 将霖天科技的路由添加到 /card/LinTianKeJi 前缀下
    ...linTianKeJiRoutes.map(route => ({
      ...route,
      path: `/card/LinTianKeJi${route.path}`
    })),
    // 将霖天科技2的路由添加到 /card/LinTianKeJi2 前缀下
    ...linTianKeJi2Routes.map(route => ({
      ...route,
      path: `/card/LinTianKeJi2${route.path}`
    })),
    // 将万网科技的路由添加到 /card/WanWangKeJi 前缀下
    ...wanWangKeJiRoutes.map(route => ({
      ...route,
      path: `/card/WanWangKeJi${route.path}`
    })),
   // 将AI数智推介官的路由添加到 /card/ai-sz-guide 前缀下
    ...aiSzGuideRoutes.map(route => ({
      ...route,
      path: `/card/ai-sz-guide${route.path}`
    })),
    // 将星图AI的路由添加到 /card/XingTuAI 前缀下
    ...xingTuAIRoutes.map(route => ({
      ...route,
      path: `/card/XingTuAI${route.path}`
    })),
  ],
  // 添加滚动行为控制
  scrollBehavior(_to, _from, _savedPosition) {
    // 始终滚动到顶部
    return { top: 0 }
  }
})

export default router