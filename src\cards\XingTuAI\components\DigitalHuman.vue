<script setup lang="ts">
import { ref, onMounted } from 'vue'

const mobileBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/SZR.jpeg'
const pcBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/SZR-PC.jpeg'

const isMobile = ref(false)

const checkDevice = () => {
  isMobile.value = window.innerWidth < 768
}

onMounted(() => {
  checkDevice()
  window.addEventListener('resize', checkDevice)
})
</script>

<template>
  <div class="digital-human">
    <img 
      :src="isMobile ? mobileBackgroundImage : pcBackgroundImage" 
      alt="星图AI数字人" 
      class="digital-human-image"
    />
  </div>
</template>

<style scoped>
.digital-human {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.digital-human-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

@media (min-width: 768px) {
  .digital-human-image {
    width: auto;
    height: 100%;
    min-height: 100vh;
  }
}
</style>
