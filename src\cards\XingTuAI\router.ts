import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'XingTuAIHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '星图AI名片 - 首页'
    }
  },
  {
    path: '/product-intro',
    name: 'XingTuAIProductIntro',
    component: () => import('./views/ProductIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '星图AI名片 - 产品介绍'
    }
  },
  {
    path: '/service-cases',
    name: 'XingTuAIServiceCases',
    component: () => import('./views/ServiceCasesView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '星图AI名片 - 服务案例'
    }
  },

]

export default routes
