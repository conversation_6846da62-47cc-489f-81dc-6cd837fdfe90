<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  School,
  Document,
  TrophyBase,
  Download,
  ArrowRight
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/LinTianKeJi2')
}

// 典型案例学校数据
const typicalCases = ref([
  {
    id: 1,
    name: '弋阳县试点学校',
    policySupport: '2023年4月被教育部列为全国38个信息技术支撑学生综合素质评价试点区域之一',
    highlights: [
      '全面实施"伴随性"评价改革，要求教师全员参与（每日评价1-2条）',
      '家长每周上报"五项管理"数据',
      '建立学生电子成长档案，档案填写完整率达100%',
      '2023年全国试点区域测评完成率排名第五'
    ]
  },
  {
    id: 2,
    name: '广信区清水乡中心小学',
    policySupport: '落实《深化新时代教育评价改革总体方案》"三全育人"要求',
    highlights: [
      '创新"五有若水少年"评价体系（德智体美劳多维度）',
      '教师参与率98%，家长接入"微家校"平台率达98%',
      '教学质量评估从全县第27位跃升至第6位（2022年）'
    ]
  },
  {
    id: 3,
    name: '横峰县试点学校',
    policySupport: '执行上饶市《推进新时代中小学生评价改革贯彻落实意见》',
    highlights: [
      '教师评价数据每周通报',
      '考核得分90分以上评为"先进学校"',
      '研学活动与电子档案结合，家长上传活动心得及照片'
    ]
  }
])

// 政策支持数据
const policySupports = ref([
  {
    level: '国家级政策',
    policies: [
      {
        name: '《深化新时代教育评价改革总体方案》（2020年）',
        description: '破除"五唯"评价',
        url: ''
      },
      {
        name: '教育部试点通知（2023年）',
        description: '弋阳县列为全国试点',
        url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/JiaoYuBu.pdf'
      }
    ]
  },
  {
    level: '省级/市级政策',
    policies: [
      {
        name: '上饶市评价改革意见（2022年）',
        description: '建立多维度评价体系，明确教师每日评价、家长每周上报要求',
        url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/RaoShuZi.pdf'
      },
      {
        name: '弋阳县考核方案（2022年）',
        description: '平台使用数据纳入考核，LED屏公示排名，对考核低于60分学校约谈',
        url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/YiYangZhongXiaoXueKaoHeFangAn.pdf'
      }
    ]
  },
  {
    level: '县级落地政策',
    policies: [
      {
        name: '横峰县考核方案（2023年）',
        description: '教师评价率、家长提交率要求100%，电子档案缺项扣分，考核结果与评优挂钩',
        url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/HengFeng.pdf'
      }
    ]
  }
])

// 关键成效数据
const keyAchievements = ref([
  {
    title: '家校协同强化',
    description: '家长通过平台参与"五项管理"，形成家校共育闭环'
  },
  {
    title: '数据驱动管理',
    description: '自动生成班级、寝室排名，提供评优依据'
  },
  {
    title: '政策标杆效应',
    description: '弋阳县成为全国改革样板'
  }
])

// 下载文档
const downloadDocument = (url: string, name: string) => {
  if (url) {
    window.open(url, '_blank')
  } 
}
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>服务案例</h1>
    </div>

    <div class="content">
      <!-- 典型案例学校 -->
      <section class="section">
        <div class="section-header">
          <el-icon class="section-icon"><School /></el-icon>
          <h2>服务案例学校</h2>
        </div>

        <div class="cases-grid">
          <div v-for="case_ in typicalCases" :key="case_.id" class="case-card">
            <div class="case-header">
              <h3>{{ case_.name }}</h3>
            </div>
            <div class="case-content">
              <div class="policy-support">
                <h4>政策支持：</h4>
                <p>{{ case_.policySupport }}</p>
              </div>
              <div class="highlights">
                <h4>实践亮点：</h4>
                <ul>
                  <li v-for="highlight in case_.highlights" :key="highlight">
                    {{ highlight }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 核心政策支持 -->
      <section class="section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <h2>核心政策支持</h2>
        </div>

        <div class="policy-levels">
          <div v-for="level in policySupports" :key="level.level" class="policy-level">
            <h3 class="level-title">{{ level.level }}</h3>
            <div class="policies-list">
              <div v-for="policy in level.policies" :key="policy.name" class="policy-item">
                <div class="policy-content">
                  <h4>{{ policy.name }}</h4>
                  <p>{{ policy.description }}</p>
                </div>
                <button
                  v-if="policy.url"
                  @click="downloadDocument(policy.url, policy.name)"
                  class="download-btn"
                >
                  <el-icon><Download /></el-icon>
                  查看文档
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 关键成效 -->
      <section class="section">
        <div class="section-header">
          <el-icon class="section-icon"><TrophyBase /></el-icon>
          <h2>关键成效</h2>
        </div>

        <div class="achievements-grid">
          <div v-for="achievement in keyAchievements" :key="achievement.title" class="achievement-card">
            <h3>{{ achievement.title }}</h3>
            <p>{{ achievement.description }}</p>
          </div>
        </div>
      </section>

      <!-- 备注说明 -->
      <section class="section">
        <div class="note-card">
          <p><strong>注：</strong>以上案例与政策均来自官方文件，可直接用于产品推广或政策汇报。</p>
        </div>
      </section>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
}

.header {
  background: linear-gradient(135deg, #079d6e, #10b981);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* 章节样式 */
.section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #079d6e;
}

.section-icon {
  font-size: 1.5rem;
  color: #079d6e;
  margin-right: 0.5rem;
}

.section-header h2 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #079d6e;
}

/* 案例卡片样式 - 单行展示 */
.cases-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.case-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(7, 157, 110, 0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.case-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.case-header h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #079d6e;
  border-bottom: 1px solid rgba(7, 157, 110, 0.2);
  padding-bottom: 0.5rem;
}

.case-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.policy-support,
.highlights {
  flex: 1;
}

.case-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.case-content p {
  margin: 0;
  line-height: 1.6;
  color: #6b7280;
  font-size: 0.9rem;
}

.case-content ul {
  margin: 0;
  padding-left: 1.2rem;
}

.case-content li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  color: #6b7280;
  font-size: 0.9rem;
}

/* 政策支持样式 */
.policy-levels {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.policy-level {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(7, 157, 110, 0.1);
}

.level-title {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #079d6e;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(7, 157, 110, 0.2);
}

.policies-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.policy-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid rgba(7, 157, 110, 0.1);
}

.policy-content {
  flex: 1;
}

.policy-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.policy-content p {
  margin: 0;
  line-height: 1.6;
  color: #6b7280;
  font-size: 0.9rem;
}

.download-btn {
  background: linear-gradient(135deg, #079d6e, #10b981);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.download-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(7, 157, 110, 0.3);
}

/* 成效卡片样式 */
.achievements-grid {
  display: grid;
  gap: 1rem;
}

.achievement-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(7, 157, 110, 0.1);
  text-align: center;
  transition: all 0.3s ease;
}

.achievement-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.achievement-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #079d6e;
}

.achievement-card p {
  margin: 0;
  line-height: 1.6;
  color: #6b7280;
}

/* 备注卡片样式 */
.note-card {
  background: linear-gradient(135deg, rgba(7, 157, 110, 0.05), rgba(16, 185, 129, 0.05));
  border: 1px solid rgba(7, 157, 110, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
}

.note-card p {
  margin: 0;
  color: #374151;
  line-height: 1.6;
}

.note-card strong {
  color: #079d6e;
}

@media (min-width: 768px) {
  .content {
    padding-bottom: 2rem;
  }

  .case-content {
    flex-direction: row;
    gap: 2rem;
  }

  .policy-support {
    flex: 1;
  }

  .highlights {
    flex: 1.5;
  }

  .achievements-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .policy-item {
    flex-direction: row;
    align-items: center;
  }
}

@media (max-width: 767px) {
  .policy-item {
    flex-direction: column;
    align-items: stretch;
  }

  .download-btn {
    align-self: flex-start;
  }
}
</style>
