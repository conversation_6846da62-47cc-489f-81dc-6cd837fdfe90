import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'linTianKeJi2Home',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '霖天科技AI名片'
    }
  },
  {
    path: '/product-intro',
    name: 'linTianKeJi2ProductIntro',
    component: () => import('./views/ProductIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '霖天科技AI名片 - 产品介绍'
    }
  },
  {
    path: '/service-cases',
    name: 'linTianKeJi2ServiceCases',
    component: () => import('./views/ServiceCasesView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '霖天科技AI名片 - 服务案例'
    }
  }
]

export default routes
