<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'


const router = useRouter()
const route = useRoute()


const activeTab = computed(() => {
  const currentPath = route.path
  if (currentPath === '/card/LinTianKeJi2' || currentPath === '/card/LinTianKeJi2/') {
    return 'home'
  } else if (currentPath.includes('/service-cases')) {
    return 'cases'
  }
  return 'home'
})

const handleTabClick = (tab: any) => {
  router.push(tab.route)
}
</script>



<style scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(7, 157, 110, 0.1);
  display: flex;
  justify-content: space-around;
  padding: 0.5rem 0;
  z-index: 1000;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  min-width: 4rem;
}

.tab-item:hover {
  background: rgba(7, 157, 110, 0.05);
}

.tab-icon {
  font-size: 1.2rem;
  color: #9ca3af;
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.tab-text {
  font-size: 0.7rem;
  color: #9ca3af;
  transition: color 0.3s ease;
}

.tab-item.active .tab-icon,
.tab-item.active .tab-text {
  color: #079d6e;
}

.tab-item.active {
  background: rgba(7, 157, 110, 0.1);
}

@media (min-width: 768px) {
  .tab-bar {
    display: none;
  }
}
</style>
