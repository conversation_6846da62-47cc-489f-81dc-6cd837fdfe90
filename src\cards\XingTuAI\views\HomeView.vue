<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'
import DigitalHuman from '../components/DigitalHuman.vue'
import NavigationButtons from '../components/NavigationButtons.vue'
import TabBar from '../components/TabBar.vue'

// 添加打字机效果
const welcomeText = ref('')
const fullText = '技术驱动价值，数据赋能未来。'
const typingSpeed = 100
let currentIndex = 0
let typingTimer: number | null = null

const typeText = () => {
  if (currentIndex < fullText.length) {
    welcomeText.value += fullText.charAt(currentIndex)
    currentIndex++
    typingTimer = setTimeout(typeText, typingSpeed) as unknown as number
  }
}

const chatWithAI = () => {
  // 这里可以替换为实际的AI对话链接
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=9ho7aq84tu1pq7vhymum9fng', '_blank')
}

onMounted(() => {
  setTimeout(() => {
    typeText()
  }, 500)
})

onBeforeRouteLeave(() => {
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
})
</script>

<template>
  <div class="home-container">
    <div class="digital-human-container">
      <div class="digital-human-wrapper">
        <DigitalHuman />
      </div>
    </div>

    <div class="bottom-section">
      <div class="content-wrapper">
        <div class="ai-content-container">
          <!-- AI助手对话框 -->
          <div class="chat-dialog">
            <!-- 对话框头部 -->
            <div class="dialog-header">
              <div class="avatar-container">
                <div class="ai-avatar">
                  <span class="avatar-text">AI</span>
                </div>
                <div class="status-indicator"></div>
              </div>
              <div class="header-info">
                <h3 class="ai-name">星图AI智能助手</h3>
                <p class="ai-status">在线 · 随时为您服务</p>
              </div>
            </div>
            
            <!-- 对话内容区域 -->
            <div class="dialog-content">
              <div class="message-bubble">
                <div class="message-text">
                  {{ welcomeText }}<span class="cursor" v-if="welcomeText.length < fullText.length">|</span>
                </div>
                <div class="message-time">{{ new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) }}</div>
              </div>
            </div>
            
            <!-- 对话框底部操作区 -->
            <div class="dialog-footer">
              <div class="quick-actions">
                <span class="quick-tag">产品咨询</span>
                <span class="quick-tag">技术支持</span>
                <span class="quick-tag">合作洽谈</span>
              </div>
              <el-button type="primary" class="chat-btn" @click="chatWithAI">
                <el-icon><ArrowRight /></el-icon>
                开始对话
              </el-button>
            </div>
          </div>
        </div>

        <div class="navigation-container">
          <NavigationButtons />
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.home-container {
  position: relative;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.digital-human-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.digital-human-wrapper {
  width: 100%;
  height: 100%;
}

.bottom-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.85) 15%, rgba(255, 255, 255, 0.95));
  z-index: 2;
  backdrop-filter: blur(5px);
  padding-bottom: 5rem; /* 为底部标签栏留出空间 */
  height: 45%; /* 保持手机端高度 */
}

.content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding-top: 1rem;
}

/* AI对话框样式 */
.ai-content-container {
  padding: 1rem;
  display: flex;
  justify-content: center;
}

.chat-dialog {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 420px;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 对话框头部 */
.dialog-header {
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #079d6e, #10b981);
  color: white;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.avatar-container {
  position: relative;
  flex-shrink: 0;
}

.ai-avatar {
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.avatar-text {
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 0.75rem;
  height: 0.75rem;
  background: #22c55e;
  border-radius: 50%;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

.header-info {
  flex: 1;
  min-width: 0;
}

.ai-name {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
}

.ai-status {
  margin: 0;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
}

/* 对话内容区域 */
.dialog-content {
  padding: 1.5rem;
  min-height: 120px;
  max-height: 200px;
  overflow-y: auto;
}

.message-bubble {
  background: #f8fafc;
  border-radius: 1rem 1rem 1rem 0.25rem;
  padding: 1rem;
  position: relative;
  border-left: 3px solid #079d6e;
}

.message-text {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.message-time {
  font-size: 0.75rem;
  color: #9ca3af;
  text-align: right;
}

/* 对话框底部 */
.dialog-footer {
  padding: 1rem 1.5rem 1.5rem;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
}

.quick-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.quick-tag {
  background: white;
  color: #6b7280;
  font-size: 0.75rem;
  padding: 0.4rem 0.8rem;
  border-radius: 1rem;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-tag:hover {
  background: #079d6e;
  color: white;
  border-color: #079d6e;
}

.chat-btn {
  background: linear-gradient(135deg, #079d6e, #10b981);
  border: none;
  border-radius: 1.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 4px 16px rgba(7, 157, 110, 0.3);
  transition: all 0.3s ease;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(7, 157, 110, 0.4);
}

.cursor {
  animation: blink 1s infinite;
  color: #079d6e;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.navigation-container {
  padding: 0rem 1rem 1.5rem 1rem;
}
</style>
